{"id": "plugin-SEARCH", "verb": "SEARCH", "description": "Searches DuckDuckGo for a given term and returns a list of links", "explanation": "This plugin takes a search term and returns a JSON array of search results from DuckDuckGo, including titles and URLs.", "inputDefinitions": [{"name": "searchTerm", "required": true, "type": "string", "description": "The term to search for on DuckDuckGo"}], "outputDefinitions": [{"name": "results", "required": false, "type": "array", "description": "Array of search results, each containing a title and URL"}], "language": "javascript", "entryPoint": {"main": "SEARCH.js", "files": {"SEARCH.js": "const axios = require('axios');\n\nasync function execute(input) {\n    try {\n        const { searchTerm } = input.args;\n        if (!searchTerm) {\n            throw new Error('Search term is required');\n        }\n\n        const response = await axios.get('https://api.duckduckgo.com/', {\n            params: {\n                q: searchTerm,\n                format: 'json'\n            }\n        });\n\n        const results = response.data.RelatedTopics.map(topic => ({\n            title: topic.Text,\n            url: topic.FirstURL\n        }));\n\n        return {\n            success: true,\n            resultType: 'array',\n            resultDescription: 'Search results from DuckDuckGo',\n            result: results,\n            mimeType: 'application/json'\n        };\n    } catch (error) {\n        console.error('SEARCH plugin failed:', error instanceof Error ? error.message : error);\n        return {\n            success: false,\n            resultType: 'error',\n            resultDescription: 'Error performing search',\n            result: null,\n            error: error instanceof Error ? error.message : 'An unknown error occurred',\n            mimeType: 'text/plain'\n        };\n    }\n}\n\nmodule.exports = { execute };"}}, "repository": {"type": "local"}, "security": {"permissions": [], "sandboxOptions": {}, "trust": {"signature": "i1dzx3kmoHOAyo8KtygFivE58Z2uLWBQKDBCMMMOOFIEKmY+pu+pnj9k2MnzCczHDiO6JiApdLqJNxCpuxX2WeFzWMV+ILQpcHoH5/EYrgAqp6l8Dm6gVubT09er2RPN72LdV+aL9Kz3hrB7ukBisauGCwb1pI11tbDkcF5v9EJ824/RHOY8SA5x+uh5aFRnMPl4lG6qM3YSQIhC+k1SCMrTrIVoUwEcpvl8zd7O1qFd+H5tVlWJ4aDm7mUfhG7IEoDL4PVF4QiKmwJ2LAjYqnaVNRKXPWFj66wJ/voiNo/iLrIlTMldrGHUAw4SzgqKmY/rmI3ncNFK9POwSz5hrg=="}}, "distribution": {"downloads": 0, "rating": 0}, "version": "1.0.0"}